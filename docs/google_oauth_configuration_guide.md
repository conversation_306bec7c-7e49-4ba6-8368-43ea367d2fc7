# Google OAuth Configuration Guide

## Overview
This guide provides step-by-step instructions to fix the Google Sign-In OAuth configuration error (`redirect_uri_mismatch`) in the Flutter web application.

## Problem Description
- **Error**: "Access blocked: This app's request is invalid" with error code 400 (redirect_uri_mismatch)
- **Root Cause**: Mismatch between authorized origins/redirect URIs in Google Cloud Console and actual application URLs

## Solution Steps

### 1. Access Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select the project: **bloomg-flutter** (Project ID: bloomg-flutter)
3. Navigate to **APIs & Services** > **Credentials**

### 2. Locate OAuth 2.0 Client ID
1. Find the OAuth 2.0 client ID for web application
2. Look for client ID: `1068175978703-asn9ene1hjta4lhro0ni4v0ji8l40tha.apps.googleusercontent.com`
3. Click on the client ID to edit

### 3. Update Authorized JavaScript Origins
Add the following URLs to **Authorized JavaScript origins**:

**Development:**
- `http://localhost:8080`
- `http://localhost:3000` (alternative development port)
- `http://127.0.0.1:8080`

**Production (when deployed):**
- `https://your-production-domain.com`

### 4. Update Authorized Redirect URIs
Add the following URLs to **Authorized redirect URIs**:

**Development:**
- `http://localhost:8080`
- `http://localhost:3000`
- `http://127.0.0.1:8080`

**Production (when deployed):**
- `https://your-production-domain.com`

### 5. Save Configuration
1. Click **Save** to apply the changes
2. Wait 5-10 minutes for changes to propagate

## Current Configuration Status

### ✅ Completed
- [x] Updated web/index.html with correct client ID
- [x] Fixed Flutter application configuration
- [x] Updated documentation

### 🔄 Manual Action Required
- [ ] Update Google Cloud Console authorized origins
- [ ] Update Google Cloud Console redirect URIs
- [ ] Test Google Sign-In functionality

## Testing Instructions

After updating the Google Cloud Console configuration:

1. **Clear Browser Cache**: Clear browser cache and cookies for localhost
2. **Restart Flutter App**: Stop and restart the Flutter development server
3. **Test Sign-In**: Click the "Continue with Google" button
4. **Verify Success**: Ensure no redirect_uri_mismatch error occurs

## Expected Behavior After Fix

1. User clicks "Continue with Google" button
2. Google OAuth popup opens without errors
3. User can select Google account and authorize
4. User is redirected back to the application
5. Authentication state is properly managed by BLoC

## Troubleshooting

### If Error Persists
1. **Double-check URLs**: Ensure exact match between console and application URLs
2. **Wait for Propagation**: Changes can take up to 10 minutes to take effect
3. **Check Client ID**: Verify the correct client ID is used in web/index.html
4. **Clear Cache**: Clear browser cache completely

### Common Issues
- **Case Sensitivity**: URLs are case-sensitive
- **Trailing Slashes**: Avoid trailing slashes in URLs
- **Protocol Mismatch**: Ensure http/https protocol matches exactly

## Contact Information
For additional support, refer to:
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Firebase Authentication Documentation](https://firebase.google.com/docs/auth)

---
*Last Updated: 2024-12-19*
